/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes.ts` as follows
|
| import './routes/cart'
| import './routes/customer''
|
*/

import Route from '@ioc:Adonis/Core/Route'

// Route.get('/', async () => {
//   return { hello: 'world' }
// })

// public routes webhook to shopify
Route.group(() => {
  require('../app/Routes/Webhook')
})
  .prefix('/webhooks')
  .middleware('shopify_webhook_verifier')

// public routes
Route.group(() => {
  Route.post('/files/get-signed-url', 'FilesController.getSignedUrl') // for casefinite-intl-sanity
  Route.post('/files/delete-object', 'FilesController.deleteObject') // for casefinite-intl-sanity
  Route.post('/files/get-signed-url-ergofinite', 'FilesController.getErgofiniteSignedUrl') // for ergofinite-sanity
  Route.post('/files/delete-object-ergofinite', 'FilesController.deleteErgofiniteObject') // for ergofinite-sanity
  Route.post('/files/get-signed-url-coffee', 'FilesController.getCoffeeSignedUrl') // for ergofinite-sanity
  Route.post('/files/delete-object-coffee', 'FilesController.deleteCoffeeObject') // for ergofinite-sanity
  // Route.post('/auth/sign-up', 'AuthController.create')
  Route.post('/auth/login', 'AuthController.login')
  Route.post('/auth/logout', 'AuthController.logout')
  Route.post('/discounts', 'DiscountsController.createPriceRuleDiscount')
  Route.post('/recruitments', 'RecruitmentsController.create')
  Route.post('/contacts', 'ContactsController.create')
  Route.post('/files', 'FilesController.upload')
  Route.get('/reviews', 'ReviewsController.find')
  Route.get('/reviews/overview', 'ReviewsController.findOverview')
  Route.post('/reviews', 'ReviewsController.createByShopifyId')
  require('../app/Routes/ShopifyCustomer')

  //routes for email review
  Route.post('/email-reviews', 'ReviewsController.emailReview')

  //routes for feedback
  Route.post('/feedbacks', 'FeedbacksController.create')
  Route.group(() => {
    Route.get('/feedbacks/:id', 'FeedbacksController.customerFindOne')
    Route.post('/feedbacks/:id', 'FeedbacksController.customerCreateHistory')
  }).middleware('customer_feedback')

  //routes for reply
  Route.group(() => {
    Route.get('code', 'YahooApisController.authCallback')
    Route.get('token', 'YahooApisController.tokenCallback')
  }).prefix('yahoo')

  Route.post('/klaviyo', 'KlaviyosController.subscribe')
}).prefix('/api/v1')

// public admin routes
Route.group(() => {
  Route.post('/auth/login', 'AuthController.loginAdmin')
}).prefix('/api/v1/admin')

// admin routes
Route.group(() => {
  Route.post('/auth/logout', 'AuthController.logout')
  Route.get('/users', 'UsersController.find')
  Route.get('/users/:id', 'UsersController.findOne')
  Route.put('/permissions/:id', 'UsersController.updatePermission')
  Route.post('/files', 'FilesController.upload')
  require('../app/Routes/Customer')
  require('../app/Routes/Stock') // admin use
  require('../app/Routes/Warehouse') // admin use
  require('../app/Routes/Product') // admin use
  require('../app/Routes/ProductVariant')
  require('../app/Routes/Event') // admin use
  require('../app/Routes/ReturnStatus') // admin use
  require('../app/Routes/Recruitment') // admin use
  require('../app/Routes/Contact') // admin use
  require('../app/Routes/Status') // app use
  require('../app/Routes/Order') // app use
  require('../app/Routes/Source') // app use
  require('../app/Routes/Review') // app use
  require('../app/Routes/Blacklist') //app use
  require('../app/Routes/YamatoSetting') //app use
  require('../app/Routes/FulfillmentSetting') //app use
  require('../app/Routes/ShopifySetting') //app use
  require('../app/Routes/AppSetting') //app use
  require('../app/Routes/Feedback') //app use
  require('../app/Routes/Fulfillment') //app use
  require('../app/Routes/PurchaseOrder') //app use
  require('../app/Routes/PurchaseOrderFulfillment') //app use
  Route.get('/event-types', 'EventsController.getTypes') // app use
  Route.get('/stock-qualities', 'StocksController.getQualities') // app use
  Route.get('/transaction-statusus', 'OrderController.getTransactionTypes') // app use
  Route.get('/fulfillment-statusus', 'OrderController.getFulfillmentTypes') // app use
  Route.get('/order-statusus', 'OrderController.getOrderTypes') // app use
  Route.get('/revenue', 'OrderController.getRevenue') // app use
  Route.get('/shopify-locations', 'ShopifySettingsController.getLocations')
  Route.get('/shopify-token', 'SourcesController.getShopifyToken')

  //reports
  Route.group(() => {
    Route.post('/fulfillment', 'ReportsController.fulfillmentReport')
    Route.post('/pending-fulfillment', 'ReportsController.pendingFulfillmentReport')
  }).prefix('reports')

  //template
  Route.get('/email-templates', 'OrderEmailsController.find')

  //scraping
  Route.post('/scrape', 'PuppeteersController.scrape')

  //routes for testing
  Route.group(() => {
    Route.post('/emails', 'EmailTestsController.send')
  }).prefix('/test')
})
  .prefix('/api/v1/admin')
  .middleware(['auth', 'acl'])
